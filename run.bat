@echo off
chcp 65001 >nul
echo 自动视频播放器
echo ==================
echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo Python环境正常
echo.
echo 正在检查依赖包...
pip show selenium >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 依赖包安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo 依赖包检查完成
echo.
echo 启动程序...
echo.
python simple_auto_clicker.py
pause
