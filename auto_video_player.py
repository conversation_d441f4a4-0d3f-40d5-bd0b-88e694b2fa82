#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动视频播放器
实时监控浏览器页面，自动点击"下一节"按钮和播放按钮
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import cv2
import numpy as np
from PIL import Image
import io

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_video_player.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AutoVideoPlayer:
    def __init__(self, chrome_driver_path=None):
        """
        初始化自动视频播放器
        
        Args:
            chrome_driver_path: ChromeDriver路径，如果为None则使用系统PATH中的chromedriver
        """
        self.driver = None
        self.wait = None
        self.chrome_driver_path = chrome_driver_path
        self.is_running = False
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 保持浏览器打开，不使用无头模式
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if self.chrome_driver_path:
                service = Service(self.chrome_driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
                
            # 隐藏自动化标识
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome浏览器驱动设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置Chrome浏览器驱动失败: {e}")
            return False
    
    def find_next_button(self):
        """
        查找"下一节"按钮
        通过多种方式查找蓝底白字的"下一节"按钮
        """
        try:
            # 方法1: 通过文本内容查找
            next_button_selectors = [
                "//button[contains(text(), '下一节')]",
                "//a[contains(text(), '下一节')]",
                "//div[contains(text(), '下一节')]",
                "//span[contains(text(), '下一节')]",
                "//*[contains(text(), '下一节') and (name()='button' or name()='a' or name()='div')]"
            ]
            
            for selector in next_button_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        # 检查元素是否可见和可点击
                        if element.is_displayed() and element.is_enabled():
                            # 检查背景色是否为蓝色（简单判断）
                            bg_color = element.value_of_css_property('background-color')
                            color = element.value_of_css_property('color')
                            logger.info(f"找到候选按钮，背景色: {bg_color}, 文字色: {color}")
                            return element
                except:
                    continue
            
            # 方法2: 通过CSS类名查找（常见的按钮类名）
            css_selectors = [
                "button[class*='next']",
                "a[class*='next']",
                ".next-btn",
                ".btn-next",
                "[class*='next-button']"
            ]
            
            for selector in css_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            if "下一节" in text:
                                return element
                except:
                    continue
                    
            return None
            
        except Exception as e:
            logger.error(f"查找下一节按钮时出错: {e}")
            return None
    
    def find_play_button(self):
        """
        查找视频播放按钮
        查找视频中央的三角播放按钮
        """
        try:
            # 常见的播放按钮选择器
            play_button_selectors = [
                "//button[contains(@class, 'play')]",
                "//div[contains(@class, 'play')]",
                "//button[contains(@aria-label, 'play') or contains(@aria-label, '播放')]",
                "//*[name()='svg' and contains(@class, 'play')]/..",
                "//button[@title='播放' or @title='Play']",
                ".video-play-btn",
                ".play-btn",
                ".vjs-big-play-button",
                ".prism-big-play-btn"
            ]
            
            for selector in play_button_selectors:
                try:
                    if selector.startswith("//") or selector.startswith("/"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        
                    for element in elements:
                        if element.is_displayed():
                            # 检查是否在视频区域中央
                            location = element.location
                            size = element.size
                            logger.info(f"找到播放按钮候选，位置: {location}, 大小: {size}")
                            return element
                except:
                    continue
            
            # 如果没找到，尝试查找video元素并点击中央
            try:
                video_elements = self.driver.find_elements(By.TAG_NAME, "video")
                if video_elements:
                    video = video_elements[0]
                    if video.is_displayed():
                        logger.info("找到video元素，将点击中央位置")
                        return video
            except:
                pass
                
            return None
            
        except Exception as e:
            logger.error(f"查找播放按钮时出错: {e}")
            return None
    
    def click_element_safely(self, element, element_name="元素"):
        """
        安全地点击元素
        """
        try:
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # 尝试直接点击
            element.click()
            logger.info(f"成功点击{element_name}")
            return True
            
        except Exception as e:
            try:
                # 如果直接点击失败，尝试使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", element)
                logger.info(f"使用JavaScript成功点击{element_name}")
                return True
            except Exception as e2:
                logger.error(f"点击{element_name}失败: {e}, JavaScript点击也失败: {e2}")
                return False
    
    def monitor_and_auto_play(self, url=None, check_interval=2):
        """
        监控页面并自动播放
        
        Args:
            url: 要监控的网页URL，如果为None则监控当前页面
            check_interval: 检查间隔（秒）
        """
        if not self.driver:
            logger.error("浏览器驱动未初始化")
            return False
            
        try:
            if url:
                logger.info(f"正在打开网页: {url}")
                self.driver.get(url)
                time.sleep(3)
            
            self.is_running = True
            logger.info("开始监控页面...")
            
            while self.is_running:
                try:
                    # 查找"下一节"按钮
                    next_button = self.find_next_button()
                    
                    if next_button:
                        logger.info("发现'下一节'按钮，准备点击...")
                        
                        if self.click_element_safely(next_button, "'下一节'按钮"):
                            # 等待页面加载
                            time.sleep(3)
                            
                            # 查找并点击播放按钮
                            play_button = self.find_play_button()
                            if play_button:
                                logger.info("发现播放按钮，准备点击...")
                                time.sleep(1)
                                self.click_element_safely(play_button, "播放按钮")
                            else:
                                logger.info("未找到播放按钮，可能视频已自动播放")
                        
                        # 点击后等待较长时间，避免重复点击
                        time.sleep(10)
                    
                    time.sleep(check_interval)
                    
                except KeyboardInterrupt:
                    logger.info("收到中断信号，停止监控")
                    break
                except Exception as e:
                    logger.error(f"监控过程中出错: {e}")
                    time.sleep(check_interval)
            
        except Exception as e:
            logger.error(f"监控失败: {e}")
            return False
        
        return True
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        logger.info("停止监控")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器已关闭")
            except:
                pass


def main():
    """主函数"""
    print("自动视频播放器")
    print("=" * 50)
    
    # 创建自动播放器实例
    player = AutoVideoPlayer()
    
    try:
        # 设置浏览器驱动
        if not player.setup_driver():
            print("浏览器驱动设置失败，请确保已安装ChromeDriver")
            return
        
        print("浏览器已启动，请手动导航到目标网页")
        print("然后按Enter键开始监控...")
        input()
        
        # 开始监控
        print("开始监控页面，按Ctrl+C停止...")
        player.monitor_and_auto_play(check_interval=2)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        player.close()


if __name__ == "__main__":
    main()
