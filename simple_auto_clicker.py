#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版自动点击器
专门用于自动点击"下一节"按钮和播放按钮
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleAutoClicker:
    def __init__(self):
        self.driver = None
        self.is_running = False
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            options = Options()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("浏览器设置完成")
            return True
        except Exception as e:
            logger.error(f"浏览器设置失败: {e}")
            return False
    
    def find_and_click_next_button(self):
        """查找并点击下一节按钮"""
        try:
            # 多种方式查找"下一节"按钮
            selectors = [
                "//button[contains(text(), '下一节')]",
                "//a[contains(text(), '下一节')]", 
                "//div[contains(text(), '下一节') and (@role='button' or contains(@class, 'btn'))]",
                "//*[contains(text(), '下一节') and (contains(@class, 'button') or contains(@class, 'btn'))]"
            ]
            
            for selector in selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        # 检查是否是蓝色背景
                        try:
                            bg_color = element.value_of_css_property('background-color')
                            # 蓝色背景的RGB值通常包含较高的蓝色分量
                            if 'rgb' in bg_color.lower():
                                logger.info(f"找到下一节按钮，背景色: {bg_color}")
                                
                                # 滚动到元素位置
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(0.5)
                                
                                # 点击按钮
                                element.click()
                                logger.info("成功点击下一节按钮")
                                return True
                        except:
                            # 如果无法获取样式，直接尝试点击
                            try:
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(0.5)
                                element.click()
                                logger.info("成功点击下一节按钮")
                                return True
                            except:
                                continue
            
            return False
        except Exception as e:
            logger.error(f"查找下一节按钮失败: {e}")
            return False
    
    def find_and_click_play_button(self):
        """查找并点击播放按钮"""
        try:
            # 等待页面加载
            time.sleep(2)
            
            # 多种方式查找播放按钮
            selectors = [
                # 通用播放按钮选择器
                "button[class*='play']",
                "div[class*='play'][role='button']",
                ".vjs-big-play-button",
                ".prism-big-play-btn",
                "button[aria-label*='播放']",
                "button[aria-label*='play']",
                # SVG播放图标
                "button svg[class*='play']",
                "div svg[class*='play']"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # 获取元素位置，确保在视频区域
                            location = element.location
                            size = element.size
                            
                            # 点击播放按钮
                            try:
                                element.click()
                                logger.info("成功点击播放按钮")
                                return True
                            except:
                                # 尝试JavaScript点击
                                self.driver.execute_script("arguments[0].click();", element)
                                logger.info("使用JavaScript成功点击播放按钮")
                                return True
                except:
                    continue
            
            # 如果找不到播放按钮，尝试点击video元素中央
            try:
                videos = self.driver.find_elements(By.TAG_NAME, "video")
                if videos:
                    video = videos[0]
                    if video.is_displayed():
                        # 点击视频中央
                        ActionChains(self.driver).move_to_element(video).click().perform()
                        logger.info("点击视频中央位置")
                        return True
            except:
                pass
            
            return False
        except Exception as e:
            logger.error(f"查找播放按钮失败: {e}")
            return False
    
    def start_monitoring(self, check_interval=3):
        """开始监控"""
        if not self.driver:
            logger.error("浏览器未初始化")
            return
        
        self.is_running = True
        logger.info("开始监控页面...")
        
        while self.is_running:
            try:
                # 查找并点击下一节按钮
                if self.find_and_click_next_button():
                    # 等待页面加载
                    time.sleep(3)
                    
                    # 查找并点击播放按钮
                    self.find_and_click_play_button()
                    
                    # 点击成功后等待较长时间，避免重复操作
                    logger.info("操作完成，等待下一次检查...")
                    time.sleep(15)
                else:
                    # 没找到下一节按钮，继续监控
                    time.sleep(check_interval)
                    
            except KeyboardInterrupt:
                logger.info("用户中断程序")
                break
            except Exception as e:
                logger.error(f"监控过程出错: {e}")
                time.sleep(check_interval)
    
    def stop(self):
        """停止监控"""
        self.is_running = False
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")


def main():
    print("简化版自动点击器")
    print("=" * 40)
    print("功能：自动点击'下一节'按钮和播放按钮")
    print("=" * 40)
    
    clicker = SimpleAutoClicker()
    
    try:
        # 设置浏览器
        if not clicker.setup_browser():
            print("浏览器设置失败")
            return
        
        print("\n浏览器已启动！")
        print("请手动打开目标网页，然后按Enter开始监控...")
        input()
        
        print("开始监控，按Ctrl+C停止...")
        clicker.start_monitoring(check_interval=3)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        clicker.stop()


if __name__ == "__main__":
    main()
