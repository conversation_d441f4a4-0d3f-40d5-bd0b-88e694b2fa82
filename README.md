# 自动视频播放器

这是一个用于自动监控浏览器页面并点击"下一节"按钮和播放按钮的Python程序。

## 功能特点

- 实时监控浏览器页面
- 自动识别蓝底白字的"下一节"按钮
- 自动点击"下一节"按钮
- 自动点击视频播放按钮（三角形播放图标）
- 支持多种按钮识别方式
- 日志记录功能

## 安装要求

### 1. Python环境
确保已安装Python 3.7或更高版本。

### 2. 安装依赖包
```bash
pip install -r requirements.txt
```

### 3. 安装ChromeDriver
下载并安装ChromeDriver：
1. 访问 https://chromedriver.chromium.org/
2. 下载与你的Chrome浏览器版本匹配的ChromeDriver
3. 将chromedriver.exe放到系统PATH中，或放到项目目录下

## 使用方法

### 方法1：使用简化版（推荐）
```bash
python simple_auto_clicker.py
```

### 方法2：使用完整版
```bash
python auto_video_player.py
```

## 使用步骤

1. 运行程序
2. 程序会自动打开Chrome浏览器
3. 手动导航到目标学习网页
4. 回到命令行，按Enter键开始监控
5. 程序会自动监控页面，当出现"下一节"按钮时自动点击
6. 点击"下一节"后会自动点击播放按钮
7. 按Ctrl+C停止程序

## 程序说明

### simple_auto_clicker.py（推荐使用）
- 简化版本，专门针对你的需求优化
- 代码更简洁，运行更稳定
- 包含多种按钮识别策略

### auto_video_player.py（完整版）
- 功能更全面的版本
- 包含更多的错误处理和日志功能
- 支持更多的自定义选项

## 注意事项

1. **浏览器兼容性**：程序使用Chrome浏览器，请确保已安装Chrome
2. **网页结构**：程序会尝试多种方式识别按钮，但如果网页结构特殊可能需要调整
3. **运行权限**：确保程序有权限控制浏览器
4. **网络延迟**：程序会等待页面加载，如果网络较慢可能需要调整等待时间

## 故障排除

### 1. ChromeDriver错误
- 确保ChromeDriver版本与Chrome浏览器版本匹配
- 确保ChromeDriver在系统PATH中或项目目录下

### 2. 找不到按钮
- 检查网页是否完全加载
- 确认按钮文本确实是"下一节"
- 可以查看日志了解详细信息

### 3. 点击失败
- 可能是按钮被其他元素遮挡
- 程序会尝试多种点击方式
- 可以手动调整等待时间

## 自定义配置

如果需要调整程序行为，可以修改以下参数：

- `check_interval`：检查间隔时间（秒）
- 等待时间：各种操作之间的等待时间
- 选择器：按钮识别的CSS选择器或XPath

## 日志文件

程序运行时会生成日志文件 `auto_video_player.log`，记录详细的运行信息，便于调试。

## 安全提示

- 本程序仅用于学习自动化，请遵守网站使用条款
- 不要在重要网站上使用，避免意外操作
- 建议先在测试环境中验证功能
