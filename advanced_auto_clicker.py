#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级自动点击器
使用图像识别和多种策略来识别按钮
"""

import time
import logging
import cv2
import numpy as np
from PIL import Image
import io
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)


class AdvancedAutoClicker:
    def __init__(self):
        self.driver = None
        self.is_running = False
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            options = Options()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("浏览器设置完成")
            return True
        except Exception as e:
            logger.error(f"浏览器设置失败: {e}")
            return False
    
    def is_blue_background(self, element):
        """检查元素是否有蓝色背景"""
        try:
            bg_color = element.value_of_css_property('background-color')
            if 'rgb' in bg_color:
                # 提取RGB值
                rgb_values = bg_color.replace('rgb(', '').replace(')', '').split(',')
                if len(rgb_values) >= 3:
                    r, g, b = map(int, rgb_values[:3])
                    # 判断是否为蓝色（蓝色分量较高，红绿分量较低）
                    if b > 100 and b > r and b > g:
                        logger.info(f"检测到蓝色背景: RGB({r}, {g}, {b})")
                        return True
            return False
        except:
            return False
    
    def capture_element_screenshot(self, element):
        """截取元素截图"""
        try:
            # 获取元素位置和大小
            location = element.location
            size = element.size
            
            # 截取整个页面
            screenshot = self.driver.get_screenshot_as_png()
            image = Image.open(io.BytesIO(screenshot))
            
            # 裁剪元素区域
            left = location['x']
            top = location['y']
            right = left + size['width']
            bottom = top + size['height']
            
            element_image = image.crop((left, top, right, bottom))
            return element_image
        except:
            return None
    
    def detect_blue_button_with_white_text(self):
        """使用图像识别检测蓝底白字按钮"""
        try:
            # 获取页面截图
            screenshot = self.driver.get_screenshot_as_png()
            image = Image.open(io.BytesIO(screenshot))
            
            # 转换为OpenCV格式
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 转换为HSV颜色空间，更容易检测蓝色
            hsv = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2HSV)
            
            # 定义蓝色范围
            lower_blue = np.array([100, 50, 50])
            upper_blue = np.array([130, 255, 255])
            
            # 创建蓝色掩码
            mask = cv2.inRange(hsv, lower_blue, upper_blue)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 筛选可能的按钮区域
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 10000:  # 按钮大小范围
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 检查长宽比，按钮通常是矩形
                    aspect_ratio = w / h
                    if 1.5 < aspect_ratio < 5:
                        # 计算点击位置（元素中心）
                        click_x = x + w // 2
                        click_y = y + h // 2
                        
                        logger.info(f"检测到可能的蓝色按钮区域: ({x}, {y}, {w}, {h})")
                        return (click_x, click_y)
            
            return None
        except Exception as e:
            logger.error(f"图像识别失败: {e}")
            return None
    
    def find_next_button_advanced(self):
        """高级方式查找下一节按钮"""
        try:
            # 方法1: 传统DOM查找
            selectors = [
                "//button[contains(text(), '下一节')]",
                "//a[contains(text(), '下一节')]",
                "//div[contains(text(), '下一节') and (@role='button' or contains(@class, 'btn'))]",
                "//*[contains(text(), '下一节')]"
            ]
            
            candidates = []
            
            for selector in selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        # 检查是否有蓝色背景
                        if self.is_blue_background(element):
                            candidates.append(element)
                        else:
                            # 即使没有蓝色背景也加入候选，但优先级较低
                            candidates.append(element)
            
            # 返回第一个候选
            if candidates:
                return candidates[0]
            
            # 方法2: 图像识别
            blue_button_pos = self.detect_blue_button_with_white_text()
            if blue_button_pos:
                # 在检测到的位置查找可点击元素
                x, y = blue_button_pos
                element = self.driver.execute_script(f"return document.elementFromPoint({x}, {y});")
                if element:
                    return element
            
            return None
            
        except Exception as e:
            logger.error(f"高级查找下一节按钮失败: {e}")
            return None
    
    def click_element_advanced(self, element):
        """高级点击方法"""
        try:
            # 方法1: 滚动到元素并直接点击
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)
            
            try:
                element.click()
                logger.info("直接点击成功")
                return True
            except:
                pass
            
            # 方法2: JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", element)
                logger.info("JavaScript点击成功")
                return True
            except:
                pass
            
            # 方法3: ActionChains点击
            try:
                ActionChains(self.driver).move_to_element(element).click().perform()
                logger.info("ActionChains点击成功")
                return True
            except:
                pass
            
            # 方法4: 坐标点击
            try:
                location = element.location
                size = element.size
                x = location['x'] + size['width'] // 2
                y = location['y'] + size['height'] // 2
                
                ActionChains(self.driver).move_by_offset(x, y).click().perform()
                logger.info("坐标点击成功")
                return True
            except:
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"高级点击失败: {e}")
            return False
    
    def find_and_click_play_button_advanced(self):
        """高级方式查找并点击播放按钮"""
        try:
            time.sleep(2)  # 等待页面加载
            
            # 播放按钮选择器
            selectors = [
                "button[class*='play']",
                "div[class*='play'][role='button']",
                ".vjs-big-play-button",
                ".prism-big-play-btn",
                "button[aria-label*='播放']",
                "button[aria-label*='play']",
                "button svg[class*='play']",
                "div svg[class*='play']",
                ".video-play-btn",
                ".play-btn"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            if self.click_element_advanced(element):
                                logger.info("成功点击播放按钮")
                                return True
                except:
                    continue
            
            # 尝试点击video元素
            try:
                videos = self.driver.find_elements(By.TAG_NAME, "video")
                if videos:
                    video = videos[0]
                    if video.is_displayed():
                        if self.click_element_advanced(video):
                            logger.info("成功点击视频元素")
                            return True
            except:
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"高级播放按钮点击失败: {e}")
            return False
    
    def start_monitoring(self, check_interval=3):
        """开始监控"""
        if not self.driver:
            logger.error("浏览器未初始化")
            return
        
        self.is_running = True
        logger.info("开始高级监控模式...")
        
        while self.is_running:
            try:
                # 查找下一节按钮
                next_button = self.find_next_button_advanced()
                
                if next_button:
                    logger.info("发现下一节按钮，准备点击...")
                    
                    if self.click_element_advanced(next_button):
                        logger.info("成功点击下一节按钮")
                        
                        # 等待页面加载
                        time.sleep(3)
                        
                        # 点击播放按钮
                        if self.find_and_click_play_button_advanced():
                            logger.info("播放按钮点击成功")
                        else:
                            logger.info("未找到播放按钮或点击失败")
                        
                        # 操作完成后等待
                        time.sleep(15)
                    else:
                        logger.warning("下一节按钮点击失败")
                        time.sleep(check_interval)
                else:
                    time.sleep(check_interval)
                    
            except KeyboardInterrupt:
                logger.info("用户中断程序")
                break
            except Exception as e:
                logger.error(f"监控过程出错: {e}")
                time.sleep(check_interval)
    
    def stop(self):
        """停止监控"""
        self.is_running = False
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")


def main():
    print("高级自动点击器")
    print("=" * 40)
    print("功能：使用图像识别和多种策略自动点击按钮")
    print("=" * 40)
    
    clicker = AdvancedAutoClicker()
    
    try:
        if not clicker.setup_browser():
            print("浏览器设置失败")
            return
        
        print("\n浏览器已启动！")
        print("请手动打开目标网页，然后按Enter开始监控...")
        input()
        
        print("开始高级监控模式，按Ctrl+C停止...")
        clicker.start_monitoring(check_interval=3)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        clicker.stop()


if __name__ == "__main__":
    main()
